"use strict";
(globalThis.webpackChunk_zero_blur_extension = globalThis.webpackChunk_zero_blur_extension || []).push([
    [42],
    {
        548: (e, t, n) => {
            var l = n(935),
                o = n(294);
            const s = Boolean(!0);
            var a;
            !(function (e) {
                (e[(e.TRACE = 0)] = "TRACE"), (e[(e.DEBUG = 1)] = "DEBUG"), (e[(e.INFO = 2)] = "INFO"), (e[(e.WARN = 3)] = "WARN"), (e[(e.ERROR = 4)] = "ERROR"), (e[(e.SILENT = 5)] = "SILENT");
            })(a || (a = {}));
            const r = new (class {
                constructor() {
                    this.logLevel = s ? a.INFO : a.TRACE;
                }
                setLevel(e) {
                    this.logLevel = e;
                }
                error(...e) {
                    this.logLevel <= a.ERROR && console.error(...e);
                }
                warn(...e) {
                    this.logLevel <= a.WARN && console.warn(...e);
                }
                info(...e) {
                    this.logLevel <= a.INFO && console.info(...e);
                }
                debug(...e) {
                    this.logLevel <= a.DEBUG && console.debug(...e);
                }
                trace(...e) {
                    this.logLevel <= a.TRACE && console.trace(...e);
                }
            })();
            async function c() {
                return new Promise((e) => {
                    chrome.tabs.query({ active: !0, currentWindow: !0 }, (t) => {
                        if (!t.length) return e(void 0);
                        e(t[0].id);
                    });
                });
            }
            function i() {
                return o.createElement("div", { className: "custom-title" }, o.createElement("div", { className: "custom-title-background" }, o.createElement("img", { src: "./static/logo/zeroblur.svg", alt: "ZeroBlur" })));
            }
            var u = n(10);
            const d = "custom-styled-button";
            function m({ isToggledOn: e = !1, onClick: t, extraClassName: n }) {
                const l = d + "-v1";
                return o.createElement(
                    "button",
                    { className: (0, u.Z)("custom-button", l, n), type: "button", onClick: t },
                    o.createElement(
                        "div",
                        { className: `${l}-indicator` },
                        o.createElement("div", { className: (0, u.Z)("custom-linear-fast", `${l}-indicator-background`, { [`${l}-indicator-background-on`]: e, [`${l}-indicator-background-off`]: !e }) }),
                        o.createElement("div", { className: (0, u.Z)("custom-linear-fast", `${l}-indicator-circle`, { [`${l}-indicator-circle-on`]: e, [`${l}-indicator-circle-off`]: !e }) })
                    )
                );
            }
            function b(e) {
                const { onClick: t, extraClassName: n, text: l, title: s, disabled: a } = e;
                return o.createElement("button", { className: (0, u.Z)("custom-button", { "custom-button-disabled": a }, d, "custom-styled-button-v2", n), type: "button", onClick: t, title: s }, l);
            }
            async function g(e, t) {
                await (async function (e) {
                    return new Promise((t) => {
                        chrome.storage.local.set(e, () => t());
                    });
                })({ [e]: t });
            }
            async function E(e) {
                return (
                    await (async function (e) {
                        return new Promise((t) => {
                            chrome.storage.local.get(e, (e) => t(e));
                        });
                    })(e)
                )[e];
            }
            function h(e) {
                const { activeTabHostname: t, enabledGlobally: n, enabledForCurrentSite: l, setEnabledGlobally: s, setEnabledForCurrentSite: a, displayToggleForCurrentSite: r } = e,
                    c = "custom-settings";
                (0, o.useEffect)(() => {
                    E("blurManagerDisabled").then((e) => {
                        s(!e);
                    });
                }, []),
                    (0, o.useEffect)(() => {
                        E("blurManagerDisabledHostnames").then((e) => {
                            const n = !e.includes(t);
                            a(n);
                        });
                    }, [t]);
                const i = (0, o.useCallback)(() => {
                        const e = !n;
                        s(e), g("blurManagerDisabled", !e), e ? y() : (v(), N());
                    }, [n]),
                    u = (0, o.useCallback)(() => {
                        const e = !l;
                        a(e),
                            E("blurManagerDisabledHostnames").then((n) => {
                                e ? (n = n.filter((e) => e !== t)) : n.push(t), g("blurManagerDisabledHostnames", n);
                            }),
                            e ? y() : (v(), N());
                    }, [l, t]);
                return o.createElement(
                    "div",
                    { className: c },
                    o.createElement(
                        "div",
                        { className: `${c}-controllers` },
                        o.createElement(f, { title: r ? "Enabled globally" : "Enabled", isToggledOn: n, hoverTitle: n ? "Disable globally" : "Enable globally", onClick: i }),
                        r && o.createElement(f, { title: "Enabled for current site", isToggledOn: l, disabled: !t || !n, hoverTitle: l ? `Disable for ${t}` : `Enable for ${t}`, onClick: u })
                    )
                );
            }
            function f(e) {
                const { title: t, isToggledOn: n, hoverTitle: l, onClick: s, disabled: a } = e,
                    r = "custom-settings-toggle-controller";
                return o.createElement(
                    "div",
                    { className: (0, u.Z)("custom-button", r, { "custom-button-disabled": a, [`${r}-disabled`]: a }), title: l, onClick: s },
                    o.createElement("div", { className: `${r}-text` }, o.createElement("div", null, t)),
                    o.createElement("div", { className: "custom-button-disabled" }, o.createElement(m, { isToggledOn: n }))
                );
            }
            function v() {
                c().then((e) => {
                    e && chrome.tabs.sendMessage(e, { type: "disableEditMode" });
                });
            }
            function y() {
                c().then((e) => {
                    e && chrome.tabs.sendMessage(e, { type: "enableAllActiveBlur" });
                });
            }
            function N() {
                c().then((e) => {
                    e && chrome.tabs.sendMessage(e, { type: "disableAllActiveBlur" });
                });
            }
            function p() {
                return o.createElement(
                    "div",
                    { className: "custom-copyright" },
                    o.createElement("a", { className: "custom-link", href: "https://divbyzero.com/tools/blur-extension/", target: "_blank" }, "Help & Support"),
                    " ",
                    o.createElement("span", null, "© ", "2022")
                );
            }
            function C(e) {
                const { editBlurDisabled: t, shortcuts: n } = e,
                    l = "custom-buttons",
                    s = (0, o.useCallback)(() => {
                        c().then((e) => {
                            null != e ? (chrome.tabs.sendMessage(e, { type: "toggleEditMode" }), window.close()) : r.warn("Active tab ID is null");
                        });
                    }, []);
                let a = "Edit blur on current page";
                return (
                    n.toggleEditMode && n.toggleEditMode.shortcut && (a += ` (${n.toggleEditMode.shortcut})`),
                    o.createElement("div", { className: l }, o.createElement(b, { text: "Edit Blur", title: a, onClick: s, disabled: t, extraClassName: (0, u.Z)(`${l}-button`, { [`${l}-button-disabled`]: t }) }))
                );
            }
            function k(e) {
                const { message: t } = e;
                return t ? o.createElement("div", { className: "custom-notification" }, t) : null;
            }
            l.render(
                o.createElement(function () {
                    const [e, t] = (0, o.useState)(!0),
                        [n, l] = (0, o.useState)(!0),
                        [s, a] = (0, o.useState)(!1),
                        [u, d] = (0, o.useState)({}),
                        [m, b] = (0, o.useState)(""),
                        [g, E] = (0, o.useState)("");
                    return (
                        (0, o.useEffect)(() => {
                            (async function () {
                                const e = await c();
                                return new Promise((t, n) => {
                                    if (!e) return n("unable to get id of active tab");
                                    chrome.tabs.sendMessage(e, { type: "getCurrentHostname" }, (e) => {
                                        e && e.data ? t(e.data) : chrome.runtime.lastError ? n(chrome.runtime.lastError.message) : n("no response from content script");
                                    });
                                });
                            })()
                                .then((e) => {
                                    b(e);
                                })
                                .catch((e) => {
                                    E("The extension is not running on this page. Try to reload the page."), r.warn(e);
                                }),
                                (async function () {
                                    return new Promise((e) => {
                                        chrome.commands.getAll((t) => {
                                            const n = {};
                                            for (const e of t) {
                                                const { name: t, description: l, shortcut: o } = e;
                                                t && (n[t] = { description: l, shortcut: o });
                                            }
                                            e(n);
                                        });
                                    });
                                })().then((e) => {
                                    d(e);
                                });
                        }, []),
                        (0, o.useEffect)(() => {
                            a(!(e && n && m));
                        }, [e, n, m]),
                        o.createElement(
                            "div",
                            { className: "custom-app" },
                            o.createElement(i, null),
                            o.createElement(k, { message: g }),
                            o.createElement(h, { activeTabHostname: m, enabledGlobally: e, enabledForCurrentSite: n, setEnabledGlobally: t, setEnabledForCurrentSite: l, displayToggleForCurrentSite: !1 }),
                            o.createElement(C, { editBlurDisabled: s, shortcuts: u }),
                            o.createElement(p, null)
                        )
                    );
                }, null),
                document.getElementById("root")
            );
        },
    },
    (e) => {
        e((e.s = 548));
    },
]);
