(() => {
    "use strict";
    const e = Boolean(!0);
    var t;
    !(function (e) {
        (e[(e.TRACE = 0)] = "TRACE"), (e[(e.DEBUG = 1)] = "DEBUG"), (e[(e.INFO = 2)] = "INFO"), (e[(e.WARN = 3)] = "WARN"), (e[(e.ERROR = 4)] = "ERROR"), (e[(e.SILENT = 5)] = "SILENT");
    })(t || (t = {}));
    const n = new (class {
        constructor() {
            this.logLevel = e ? t.INFO : t.TRACE;
        }
        setLevel(e) {
            this.logLevel = e;
        }
        error(...e) {
            this.logLevel <= t.ERROR && console.error(...e);
        }
        warn(...e) {
            this.logLevel <= t.WARN && console.warn(...e);
        }
        info(...e) {
            this.logLevel <= t.INFO && console.info(...e);
        }
        debug(...e) {
            this.logLevel <= t.DEBUG && console.debug(...e);
        }
        trace(...e) {
            this.logLevel <= t.TRACE && console.trace(...e);
        }
    })();
    async function o(e) {
        return new Promise((t) => {
            chrome.storage.local.get(e, (e) => t(e));
        });
    }
    function i() {
        return new Date().toISOString();
    }
    function l() {
        return `${document.location.hostname}${document.location.pathname}`;
    }
    function r() {
        return document.location.hostname;
    }
    const s = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M16 9v10H8V9h8m-1.5-6h-5l-1 1H5v2h14V4h-3.5l-1-1zM18 7H6v12c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7z"/></svg>',
        a = "blurManagerItems",
        d = "blurManagerItemsOfPathname",
        u = "blurManagerItemsOfHostname";
    var c, h, m, p;
    !(function (e) {
        let t,
            i = 0,
            l = Promise.resolve(),
            r = [],
            s = [],
            c = [];
        e.add = async function (e, h, m, p) {
            await l,
                window.clearTimeout(i),
                r.push(e),
                h && s.push(h),
                m && c.push(m),
                null == p && (p = 250),
                (i = window.setTimeout(() => {
                    !(async function () {
                        (l = new Promise((e) => {
                            t = e;
                        })),
                            n.debug("Transaction locked");
                        let e = !0;
                        try {
                            await (async function () {
                                const e = await o({ [a]: {}, [d]: {}, [u]: {} }),
                                    t = { items: e[a], itemsOfPathname: e[d], itemsOfHostname: e[u] };
                                for (const e of r) n.debug("Transaction function started"), e(t), n.debug("Transaction function ended");
                                await (async function (e) {
                                    return new Promise((t) => {
                                        chrome.storage.local.set(e, () => t());
                                    });
                                })({ [a]: t.items, [d]: t.itemsOfPathname, [u]: t.itemsOfHostname });
                            })();
                        } catch (t) {
                            (e = !1), n.error(t), n.warn("Transaction failed");
                        }
                        if (e)
                            for (const e of s)
                                try {
                                    e();
                                } catch (e) {
                                    n.error(e);
                                }
                        else
                            for (const e of c)
                                try {
                                    e();
                                } catch (e) {
                                    n.error(e);
                                }
                        (r = []), (s = []), (c = []), t(), n.debug("Transaction unlocked");
                    })();
                }, p)),
                n.debug("Added in transaction");
        };
    })(c || (c = {})),
        (function (e) {
            function t(e, t) {
                return new Promise((o, l) => {
                    c.add(
                        (o) => {
                            const l = i();
                            let r = {};
                            try {
                                r = t.serialize();
                            } catch (e) {
                                throw (n.error("Unable to serialize:", t), e);
                            }
                            let s = o.items[e];
                            s ? ((s.updatedAt = i()), (s.data = r), n.debug("Storage item updated:", s)) : ((s = { id: e, createdAt: l, updatedAt: l, data: r }), (o.items[e] = s), n.debug("Storage item created:", s));
                        },
                        () => o(e),
                        () => l()
                    );
                });
            }
            (e.create = t),
                (e.read = function (e, t = !1) {
                    let n;
                    return new Promise((o, i) => {
                        c.add(
                            (t) => {
                                const o = t.items[e];
                                o && (n = { ...o });
                            },
                            () => o(n),
                            () => i(),
                            t ? 0 : void 0
                        );
                    });
                }),
                (e.update = function (e, n) {
                    return t(e, n);
                }),
                (e.remove = function (e) {
                    return new Promise((t, o) => {
                        c.add(
                            (t) => {
                                delete t.items[e], n.debug(`Storage item removed: ${e}`);
                            },
                            () => t(),
                            () => o()
                        );
                    });
                });
        })(h || (h = {})),
        (function (e) {
            function t(e, t) {
                return new Promise((o, i) => {
                    c.add(
                        (o) => {
                            const i = o.itemsOfPathname[e];
                            i ? i.includes(t) || i.push(t) : (o.itemsOfPathname[e] = [t]), n.debug(`Storage item - ${t} -`, `linked with pathname - ${e}`);
                        },
                        () => o(e),
                        () => i()
                    );
                });
            }
            (e.create = t),
                (e.read = function (e, t = !1) {
                    let n;
                    return new Promise((o, i) => {
                        c.add(
                            (t) => {
                                const o = t.itemsOfPathname[e];
                                o && (n = [...o]);
                            },
                            () => o(n),
                            () => i(),
                            t ? 0 : void 0
                        );
                    });
                }),
                (e.update = function (e, n) {
                    return t(e, n);
                }),
                (e.remove = function (e, t) {
                    return new Promise((o, i) => {
                        c.add(
                            (o) => {
                                let i = o.itemsOfPathname[e];
                                i && ((i = i.filter((e) => e !== t)), i.length ? (o.itemsOfPathname[e] = i) : delete o.itemsOfPathname[e]), n.debug(`Storage item - ${t} -`, `unlinked with pathname - ${e}`);
                            },
                            () => o(),
                            () => i()
                        );
                    });
                });
        })(m || (m = {})),
        (function (e) {
            function t(e, t) {
                return new Promise((o, i) => {
                    c.add(
                        (o) => {
                            const i = o.itemsOfHostname[e];
                            i ? i.includes(t) || i.push(t) : (o.itemsOfHostname[e] = [t]), n.debug(`Storage item - ${t} -`, `linked with hostname - ${e}`);
                        },
                        () => o(e),
                        () => i()
                    );
                });
            }
            (e.create = t),
                (e.read = function (e, t = !1) {
                    let n;
                    return new Promise((o, i) => {
                        c.add(
                            (t) => {
                                const o = t.itemsOfHostname[e];
                                o && (n = [...o]);
                            },
                            () => o(n),
                            () => i(),
                            t ? 0 : void 0
                        );
                    });
                }),
                (e.update = function (e, n) {
                    return t(e, n);
                }),
                (e.remove = function (e, t) {
                    return new Promise((o, i) => {
                        c.add(
                            (o) => {
                                let i = o.itemsOfHostname[e];
                                i && ((i = i.filter((e) => e !== t)), i.length ? (o.itemsOfHostname[e] = i) : delete o.itemsOfHostname[e]), n.debug(`Storage item - ${t} -`, `unlinked with hostname - ${e}`);
                            },
                            () => o(),
                            () => i()
                        );
                    });
                });
        })(p || (p = {}));
    let g = 10;
    var f, b, v, w, y, T, B, P, E, C, A, L;
    !(function (e) {
        (e.create = function () {
            return `${new Date().getTime().toString()}:${Math.random().toString().substr(2, 3)}`;
        }),
            (e.createNull = function () {
                return "-1";
            }),
            (e.isNull = function (e) {
                return "-1" === e;
            });
    })(f || (f = {})),
        (function (e) {
            let t = null,
                o = null;
            function i(e) {
                const t = document.createElement("link");
                return t.setAttribute("rel", "stylesheet"), t.setAttribute("type", "text/css"), t.setAttribute("href", "data:text/css;charset=UTF-8," + encodeURIComponent(e)), t;
            }
            (e.hide = function () {
                if (document.body) document.body.style.visibility = "hidden";
                else {
                    const e = i("body {visibility: hidden}");
                    document.head.appendChild(e), (t = e);
                }
                n.debug("Document was hidden");
            }),
                (e.unhide = function () {
                    if ((document.body && (document.body.style.visibility = ""), t)) {
                        const e = t.parentElement;
                        null == e || e.removeChild(t), (t = null);
                    }
                    n.debug("Document was unhidden");
                }),
                (e.blur = function () {
                    if (document.body) document.body.style.filter = "blur(10px)";
                    else {
                        const e = i("body {filter: blur(10px)}");
                        document.head.appendChild(e), (o = e);
                    }
                    n.debug("Document was blurred");
                }),
                (e.unblur = function () {
                    if ((document.body && (document.body.style.filter = ""), o)) {
                        const e = o.parentElement;
                        null == e || e.removeChild(o), (o = null);
                    }
                    n.debug("Document was unblurred");
                });
        })(b || (b = {})),
        (function (e) {
            let t = "",
                o = "";
            function i() {
                return !!t;
            }
            function l() {
                let e = document.querySelector("link[rel~='icon']");
                return e || ((e = document.createElement("link")), (e.rel = "icon"), document.getElementsByTagName("head")[0].appendChild(e)), e;
            }
            (e.blur = function () {
                if (i()) return void n.debug("Document title already blurred, skipped");
                (t = document.title), (document.title = "‎");
                const e = l();
                (o = e.href ? e.href : `${document.location.origin}/favicon.ico`), (e.href = chrome.runtime.getURL("static/logo/favicon.ico"));
            }),
                (e.unblur = function () {
                    i() ? ((document.title = t), (t = ""), (l().href = o), (o = "")) : n.debug("Document title not blurred, skipped");
                }),
                (e.isBlurred = i);
        })(v || (v = {})),
        (function (e) {
            let t = [];
            function o(e) {
                return t.find((t) => t.point.type === e.type && t.point.element === e.element);
            }
            (e.push = function (e) {
                const i = o(e);
                if (i) return n.debug("Already exists in pending buffer:", i), i.id;
                const l = f.create(),
                    r = { id: l, point: e };
                return t.push(r), n.debug("Added to pending buffer:", r), n.debug(t), l;
            }),
                (e.find = o),
                (e.clear = function () {
                    (t = []), n.debug("Pending buffer was cleared");
                }),
                (e.release = function () {
                    for (const e of t) {
                        const t = y.findWithPoint(e.point);
                        t ? (y.disable(t.id), y.remove(t.id)) : (y.push(e.id, e.point), y.enable(e.id), y.unhideControllers(e.id));
                    }
                    n.debug("Pending buffer was released");
                });
        })(w || (w = {})),
        (function (e) {
            let t;
            !(function (e) {
                (e.HOST = "host"), (e.PATH = "path");
            })((t = e.ApplyTo || (e.ApplyTo = {})));
            class o {
                constructor() {
                    (this.id = f.createNull()), (this.enabled = !1), (this.point = new x()), (this.applyTo = t.PATH), (this.intensity = g);
                }
                serialize() {
                    return { id: this.id, enabled: this.enabled, point: this.point.serialize(), applyTo: this.applyTo, intensity: this.intensity };
                }
                deserialize(e) {
                    (this.id = e.id), (this.enabled = e.enabled), (this.point = new x()), (this.applyTo = e.applyTo), (this.intensity = e.intensity), this.point.deserialize(e.point);
                }
            }
            e.Data = o;
            let i = [];
            function l(e) {
                return i.find((t) => t.point.type === e.type && t.point.element === e.element);
            }
            function r(e) {
                return i.find((t) => t.id === e);
            }
            function s(e, t = !0) {
                const o = i.length;
                i = i.filter((t) => {
                    const n = t.id === e;
                    if (n) {
                        if (t.point.shouldBeRemovedFromDOM && t.point.element) {
                            const e = t.point.element.parentElement;
                            e && e.removeChild(t.point.element);
                        }
                        T.remove(e);
                    }
                    return !n;
                });
                const l = i.length,
                    r = o !== l;
                return (
                    r && (n.debug("Removed from active buffer:", e), n.debug(i)), o - l > 1 && n.debug("More than one data was removed from active buffer.", "It is not logic error?"), t && (h.remove(e), m.remove($, e), p.remove(D, e)), r
                );
            }
            function a(e, t = !0) {
                const o = r(e);
                if (!o) return n.debug("Unable to enable, data is missing:", e), !1;
                const { point: i } = o;
                if (!i.element) return n.debug("Unable to enable, element is null:", o), !1;
                const { intensity: l } = o,
                    s = i.element.style;
                return (
                    i.isTransparent ? (s.backdropFilter = `blur(${l}px)`) : (s.filter = `blur(${l}px)`),
                    i.classNameToHide && i.element && i.element.classList.remove(i.classNameToHide),
                    (o.enabled = !0),
                    n.debug("Enabled:", o),
                    t && h.update(e, o),
                    !0
                );
            }
            function d(e, t = !0) {
                const o = r(e);
                if (!o) return n.debug("Unable to disable, data is missing:", e), !1;
                const { point: i } = o;
                if (!i.element) return n.debug("Unable to disable, element is null:", o), !1;
                const l = i.element.style;
                return i.isTransparent ? (l.backdropFilter = "") : (l.filter = ""), i.classNameToHide && i.element && i.element.classList.add(i.classNameToHide), (o.enabled = !1), n.debug("Disabled:", o), t && h.update(e, o), !0;
            }
            function u(e, o, i = !0) {
                const l = r(e);
                return l
                    ? ((l.applyTo = o), n.debug(`Will be applied to ${o}:`, l), i && (h.update(e, l), m.remove($, e), p.remove(D, e), o === t.HOST ? p.create(D, e) : o === t.PATH ? m.create($, e) : n.warn("Unknown target")), !0)
                    : (n.debug(`Unable to apply to ${o},`, "data is missing:", e), !1);
            }
            function c(e, t, o = !0) {
                const i = r(e);
                return i ? (t < 0 && (t = 0), (i.intensity = t), (g = t), n.debug("Intensity changed:", i), o && h.update(e, i), !0) : (n.debug("Unable to set intensity, data is missing:", e), !1);
            }
            function b(e) {
                return e.point.element ? (T.bind(e.point.element, e.id, { displayApplyToHostButton: e.applyTo === t.PATH, displayApplyToPathButton: e.applyTo === t.HOST }), !0) : (n.debug("Unable to bind control element:", e), !1);
            }
            function v(e) {
                const t = r(e);
                return t
                    ? (t.point.element && t.point.classNameToHideControllers && t.point.element.classList.remove(t.point.classNameToHideControllers),
                      t.point.element && t.point.classNameToAddOutline && t.point.element.classList.add(t.point.classNameToAddOutline),
                      T.unhide(e),
                      !0)
                    : (n.debug("Unable to unhide controllers, data is missing:", e), !1);
            }
            function w(e) {
                const t = r(e);
                return t
                    ? (t.point.element && t.point.classNameToHideControllers && t.point.element.classList.add(t.point.classNameToHideControllers),
                      t.point.element && t.point.classNameToAddOutline && t.point.element.classList.remove(t.point.classNameToAddOutline),
                      T.hide(e),
                      !0)
                    : (n.debug("Unable to hide controllers, data is missing:", e), !1);
            }
            function y(e) {
                const t = r(e);
                if (!t) return n.debug("Unable to enable interaction block, data is missing:", e), !1;
                const o = t.point.getRect();
                return B.remove(e), B.create({ id: e, width: o.width, height: o.height, left: o.left, top: o.top }), !0;
            }
            function P(e) {
                return r(e) ? (B.remove(e), !0) : (n.debug("Unable to disable interaction block, data is missing:", e), !1);
            }
            (e.push = function (e, t, l = !0) {
                const r = new o();
                return (r.id = e), (r.point = t), i.push(r), n.debug("Added to active buffer:", r), n.debug(i), l && (h.create(e, r), u(r.id, r.applyTo)), b(r), e;
            }),
                (e.restore = function (e) {
                    return i.push(e), n.debug("Restored in active buffer:", e), n.debug(i), (g = e.intensity), e.enabled ? a(e.id, !1) : d(e.id, !1), b(e), e.id;
                }),
                (e.find = function (e) {
                    return l(e);
                }),
                (e.findWithPoint = l),
                (e.findWithId = r),
                (e.remove = s),
                (e.removeAll = function (e = !0) {
                    for (const t of i) s(t.id, e);
                }),
                (e.enable = a),
                (e.enableAll = function (e = !0) {
                    for (const t of i) a(t.id, e);
                }),
                (e.disable = d),
                (e.disableAll = function (e = !0) {
                    for (const t of i) d(t.id, e);
                }),
                (e.setPoint = function (e, t, o = !0) {
                    const i = r(e);
                    return i ? ((i.point = t), n.debug("Point changed:", i), o && h.update(e, i), !0) : (n.debug("Unable to set point, data is missing:", e), !1);
                }),
                (e.applyTo = u),
                (e.setIntensity = c),
                (e.setIntensityAll = function (e, t = !0) {
                    for (const n of i) c(n.id, e, t);
                }),
                (e.unhideControllers = v),
                (e.hideControllers = w),
                (e.unhideControllersAll = function () {
                    for (const e of i) v(e.id);
                }),
                (e.hideControllersAll = function () {
                    for (const e of i) w(e.id);
                }),
                (e.enableInteractionBlock = y),
                (e.disableInteractionBlock = P),
                (e.enableInteractionBlockAll = function () {
                    for (const e of i) y(e.id);
                }),
                (e.disableInteractionBlockAll = function () {
                    for (const e of i) P(e.id);
                }),
                (e.exists = function () {
                    return !!i.length;
                });
        })(y || (y = {})),
        (function (e) {
            const t = "custom-blur-controllers-button",
                o = "custom-blur-controllers-button-hide",
                i = "custom-blur-controllers-hide",
                l = {},
                r = {},
                a = {};
            function d(e, t) {
                const { root: n } = e,
                    o = n.getBoundingClientRect(),
                    i = t.getBoundingClientRect(),
                    l = document.body.scrollWidth,
                    r = document.body.scrollHeight;
                let s = window.scrollX + i.left + i.width / 2 - o.width / 2,
                    a = window.scrollY + i.top + i.height / 2 - o.height / 2;
                (o.height >= i.height || o.width >= i.width) && (a = window.scrollY + i.top + i.height + 5),
                    a + o.height > r && (a = window.scrollY + i.top - o.height - 5),
                    s + o.width > l && (s = l - o.width),
                    s < 0 && (s = 0),
                    a < 0 && (a = 0),
                    (n.style.left = `${s}px`),
                    (n.style.top = `${a}px`);
            }
            (e.bind = function (e, u, c = {}) {
                const h = (function (e) {
                    const n = document.createElement("div"),
                        i = document.createElement("div"),
                        l = document.createElement("div"),
                        r = document.createElement("span"),
                        a = document.createElement("span"),
                        d = document.createElement("span");
                    return (
                        n.classList.add("custom-blur-controllers"),
                        i.classList.add("custom-blur-controllers-container"),
                        l.classList.add("custom-blur-controllers-buttons"),
                        r.classList.add(t),
                        a.classList.add(t, o),
                        d.classList.add(t, o),
                        e.displayApplyToPathButton && d.classList.remove(o),
                        e.displayApplyToHostButton && a.classList.remove(o),
                        k(n),
                        k(i),
                        k(l),
                        k(r),
                        k(a),
                        k(d),
                        (r.title = "Remove this blur"),
                        (a.title = "Apply this blur to entire site"),
                        (d.title = "Apply this blur only to this page"),
                        (r.innerHTML = s),
                        (a.innerHTML =
                            '<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM4 12c0-.61.08-1.21.21-1.78L8.99 15v1c0 1.1.9 2 2 2v1.93C7.06 19.43 4 16.07 4 12zm13.89 5.4c-.26-.81-1-1.4-1.9-1.4h-1v-3c0-.55-.45-1-1-1h-6v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41C17.92 5.77 20 8.65 20 12c0 2.08-.81 3.98-2.11 5.4z"/></svg>'),
                        (d.innerHTML =
                            '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><g><rect fill="none" height="24" width="24"/><path d="M11,8.17L6.49,3.66C8.07,2.61,9.96,2,12,2c5.52,0,10,4.48,10,10c0,2.04-0.61,3.93-1.66,5.51l-1.46-1.46 C19.59,14.87,20,13.48,20,12c0-3.35-2.07-6.22-5-7.41V5c0,1.1-0.9,2-2,2h-2V8.17z M21.19,21.19l-1.41,1.41l-2.27-2.27 C15.93,21.39,14.04,22,12,22C6.48,22,2,17.52,2,12c0-2.04,0.61-3.93,1.66-5.51L1.39,4.22l1.41-1.41L21.19,21.19z M11,18 c-1.1,0-2-0.9-2-2v-1l-4.79-4.79C4.08,10.79,4,11.38,4,12c0,4.08,3.05,7.44,7,7.93V18z"/></g></svg>'),
                        n.appendChild(i),
                        i.appendChild(l),
                        l.appendChild(a),
                        l.appendChild(d),
                        l.appendChild(r),
                        document.body.appendChild(n),
                        { root: n, removeButton: r, applyToHostButton: a, applyToPathButton: d }
                    );
                })(c);
                d(h, e),
                    (function (e, t, s) {
                        const { root: u, removeButton: c, applyToHostButton: h, applyToPathButton: m } = e,
                            p = () => {
                                d(e, t);
                            },
                            g = new MutationObserver(p);
                        g.observe(t, { attributeFilter: ["style"] }),
                            window.addEventListener("resize", p),
                            (l[s] = () => {
                                g.disconnect(), window.removeEventListener("resize", p);
                                const e = u.parentElement;
                                e && e.removeChild(u), n.debug(`Controllers element for - ${s} - removed`);
                            }),
                            (r[s] = () => {
                                u.classList.add(i), n.debug(`Controllers element for - ${s} - hidden`);
                            }),
                            (a[s] = () => {
                                u.classList.remove(i), n.debug(`Controllers element for - ${s} - unhidden`);
                            }),
                            c.addEventListener("click", () => {
                                y.disable(s), y.remove(s);
                            }),
                            h.addEventListener("click", () => {
                                h.classList.add(o), m.classList.remove(o), y.applyTo(s, y.ApplyTo.HOST);
                            }),
                            m.addEventListener("click", () => {
                                m.classList.add(o), h.classList.remove(o), y.applyTo(s, y.ApplyTo.PATH);
                            });
                    })(h, e, u),
                    n.debug(`Controllers element binded to active blur - ${u} -`, "and target -", e);
            }),
                (e.remove = function (e) {
                    const t = l[e];
                    t && (t(), delete l[e], delete r[e], delete a[e]);
                }),
                (e.hide = function (e) {
                    const t = r[e];
                    t && t();
                }),
                (e.unhide = function (e) {
                    const t = a[e];
                    t && t();
                });
        })(T || (T = {})),
        (function (e) {
            const t = {};
            (e.create = function (e) {
                const { id: o, width: i, height: l, left: r, top: s } = e,
                    a = document.createElement("div");
                a.classList.add("custom-interaction-block"),
                    (a.style.width = `${i}px`),
                    (a.style.height = `${l}px`),
                    (a.style.left = `${r}px`),
                    (a.style.top = `${s}px`),
                    k(a),
                    document.body.appendChild(a),
                    (t[o] = () => {
                        const e = a.parentElement;
                        e && e.removeChild(a);
                    }),
                    n.debug(`Interaction block created for - ${o}`);
            }),
                (e.remove = function (e) {
                    const o = t[e];
                    o && (o(), delete t[e], n.debug(`Interaction block removed for - ${e}`));
                });
        })(B || (B = {})),
        (function (e) {
            const t = "custom-toolbar-spacing-small",
                o = "custom-toolbar-button";
            let i = null,
                l = null;
            function r(e, t, n, o) {
                const i = document.createElement("div");
                return e && i.classList.add(...e), t && (i.title = t), n && (i.innerHTML = n), o && (i.innerText = o), k(i), i;
            }
            function a(e, t, n) {
                n && e.addEventListener(t, n);
            }
            (e.create = function (e = {}) {
                !(function (e, t) {
                    const { root: n } = e;
                    (i = () => {
                        var e;
                        null === (e = n.parentElement) || void 0 === e || e.removeChild(n);
                    }),
                        (l = (t) => {
                            e.currentBlurIntensityText.innerText = String(t);
                        }),
                        a(e.closeButton, "click", t.onCloseButtonClick),
                        a(e.toggleElementBlurButton, "click", t.onToggleElementBlurButtonClick),
                        a(e.toggleAreaBlurButton, "click", t.onToggleAreaBlurButtonClick),
                        a(e.toggleDocumentTitleBlurButton, "click", t.onToggleDocumentTitleBlurButtonClick),
                        a(e.removeAllBlurButton, "click", t.onRemoveAllBlurButtonClick),
                        a(e.decreaseBlurIntensityButton, "click", t.onDecreaseBlurIntensityButtonClick),
                        a(e.increaseBlurIntensityButton, "click", t.onIncreaseBlurIntensityButtonClick);
                })(
                    (function () {
                        let e = "Finish editing",
                            n = "Blur element",
                            i = "Blur arbitrary area",
                            l = "Blur document title";
                        R.toggleEditMode && R.toggleEditMode.shortcut && (e += ` (${R.toggleEditMode.shortcut})`),
                            R.togglePointToElement && R.togglePointToElement.shortcut && (n += ` (${R.togglePointToElement.shortcut})`),
                            R.togglePointToArea && R.togglePointToArea.shortcut && (i += ` (${R.togglePointToArea.shortcut})`),
                            R.toggleDocumentTitleBlur && R.toggleDocumentTitleBlur.shortcut && (l += ` (${R.toggleDocumentTitleBlur.shortcut})`);
                        const a = r(["custom-toolbar"]),
                            d = r(["custom-toolbar-container", "custom-toolbar-spacing-big"]),
                            u = r(
                                [o],
                                e,
                                '<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg>'
                            ),
                            c = r(
                                [o],
                                n,
                                '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><g><rect fill="none" height="24" width="24"/><path d="M17,5h-2V3h2V5z M15,15v6l2.29-2.29L19.59,21L21,19.59l-2.29-2.29L21,15H15z M19,9h2V7h-2V9z M19,13h2v-2h-2V13z M11,21h2 v-2h-2V21z M7,5h2V3H7V5z M3,17h2v-2H3V17z M5,21v-2H3C3,20.1,3.9,21,5,21z M19,3v2h2C21,3.9,20.1,3,19,3z M11,5h2V3h-2V5z M3,9h2 V7H3V9z M7,21h2v-2H7V21z M3,13h2v-2H3V13z M3,5h2V3C3.9,3,3,3.9,3,5z"/></g></svg>'
                            ),
                            h = r(
                                [o],
                                i,
                                '<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 12h-2v3h-3v2h5v-5zM7 9h3V7H5v5h2V9zm14-6H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16.01H3V4.99h18v14.02z"/></svg>'
                            ),
                            m = r([o], l, '<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M5 4v3h5.5v12h3V7H19V4z"/></svg>'),
                            p = r([o], "Remove all blur on the page", s),
                            f = r(
                                [o],
                                "Decrease blur intensity",
                                '<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 13H5v-2h14v2z"/></svg>'
                            ),
                            b = r(
                                [o],
                                "Increase blur intensity",
                                '<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>'
                            ),
                            v = r(["custom-toolbar-text"], void 0, void 0, String(g)),
                            w = r([t]),
                            y = r([t]),
                            T = r(["custom-toolbar-separator"]);
                        return (
                            w.appendChild(c),
                            w.appendChild(h),
                            w.appendChild(m),
                            y.appendChild(b),
                            y.appendChild(v),
                            y.appendChild(f),
                            d.appendChild(w),
                            d.appendChild(p),
                            d.appendChild(y),
                            d.appendChild(T),
                            d.appendChild(u),
                            a.appendChild(d),
                            document.body.appendChild(a),
                            {
                                root: a,
                                closeButton: u,
                                toggleElementBlurButton: c,
                                toggleAreaBlurButton: h,
                                toggleDocumentTitleBlurButton: m,
                                removeAllBlurButton: p,
                                decreaseBlurIntensityButton: f,
                                increaseBlurIntensityButton: b,
                                currentBlurIntensityText: v,
                            }
                        );
                    })(),
                    e
                ),
                    n.debug("Toolbar created");
            }),
                (e.remove = function () {
                    i && (i(), (i = null), (l = null), n.debug("Toolbar removed"));
                }),
                (e.setCurrentBlurIntensityText = function (e) {
                    l && l(e);
                });
        })(P || (P = {}));
    class x {
        constructor() {
            (this.type = "empty"), (this.element = null);
        }
        serialize() {
            const e = { type: this.type };
            return (
                this.element &&
                    !this.shouldBeRemovedFromDOM &&
                    (e.element = (function (e) {
                        if (e.id) return `#${e.id}`;
                        const t = [];
                        let o = e,
                            i = !1;
                        for (; "BODY" !== o.tagName; ) {
                            const e = o.parentElement;
                            if (!e) {
                                i = !0;
                                break;
                            }
                            const n = {};
                            let l = !1;
                            for (const t of e.children) {
                                const e = t.tagName,
                                    i = n[e] || 0;
                                if (((n[e] = i + 1), t === o)) {
                                    l = !0;
                                    break;
                                }
                            }
                            if (!l) {
                                i = !0;
                                break;
                            }
                            const r = n[o.tagName],
                                s = `${o.tagName.toLowerCase()}:nth-of-type(${r})`;
                            t.push(s), (o = e);
                        }
                        if (i) throw (n.error(e), new Error("Unable to create query selector"));
                        return t.push("body"), t.reverse().join(" > ");
                    })(this.element)),
                null != this.width && (e.width = this.width),
                null != this.height && (e.height = this.height),
                null != this.left && (e.left = this.left),
                null != this.top && (e.top = this.top),
                e
            );
        }
        deserialize(e) {
            (this.type = e.type),
                (this.element = e.element ? document.querySelector(e.element) : null),
                null != e.width && (this.width = e.width),
                null != e.height && (this.height = e.height),
                null != e.left && (this.left = e.left),
                null != e.top && (this.top = e.top);
        }
        getRect() {
            const e = { width: 0, height: 0, left: 0, top: 0 };
            if (this.element) {
                const t = this.element.getBoundingClientRect();
                (e.width = t.width), (e.height = t.height), (e.left = t.left + window.scrollX), (e.top = t.top + window.scrollY);
            } else (e.width = this.width || 0), (e.height = this.height || 0), (e.left = this.left || 0), (e.top = this.top || 0);
            return e;
        }
    }
    function k(e) {
        e && (e.dataset.pointerIgnore = "true");
    }
    function H(e) {
        return !!e && !!e.dataset.pointerIgnore;
    }
    !(function (e) {
        const t = new x();
        let n = t;
        e.Pointer = class {
            on() {
                this.resetLastPoint();
            }
            off() {
                this.resetLastPoint();
            }
            getEmptyPoint() {
                return t;
            }
            getLastPoint() {
                return n;
            }
            setLastPoint(e) {
                n = e;
            }
            resetLastPoint() {
                this.setLastPoint(this.getEmptyPoint());
            }
        };
    })(E || (E = {})),
        (function (e) {
            const t = "custom-element-pointer-global-cursor",
                o = "custom-element-pointer-point";
            class i extends E.Pointer {
                point(e) {
                    const t = this.read(e),
                        o = this.getLastPoint();
                    return o.element === t ? o : (this.removePoint(), this.shouldBeIgnored(t) ? (n.debug("Ignored:", t), this.getEmptyPoint()) : (this.setPoint(t), n.debug("Pointing to new element:", t), this.getLastPoint()));
                }
                restore(e) {
                    return "empty" === e.type || null == e.element ? this.getEmptyPoint() : e;
                }
                on() {
                    document.body.classList.add(t), super.on();
                }
                off() {
                    this.removePoint(), document.body.classList.remove(t), super.off();
                }
                read(e) {
                    const t = { x: e.clientX, y: e.clientY };
                    let o = document.elementFromPoint(t.x, t.y);
                    return null == o ? n.debug(`Element at (${t.x}, ${t.y}) is null`) : "style" in o || (n.warn("Element don't supports inline style:", o), (o = null)), o;
                }
                create() {
                    throw new Error("Not supported");
                }
                setPoint(e) {
                    null == e || e.classList.add(o);
                    const t = new x();
                    (t.type = "element"), (t.element = e), this.setLastPoint(t);
                }
                removePoint() {
                    var e;
                    null === (e = this.getLastPoint().element) || void 0 === e || e.classList.remove(o), this.resetLastPoint();
                }
                shouldBeIgnored(e) {
                    return null == e || H(e);
                }
            }
            e.Pointer = i;
        })(C || (C = {})),
        (function (e) {
            const t = "custom-area-pointer-global-cursor";
            let o = null,
                i = !1,
                l = !1,
                r = null,
                s = null,
                a = null;
            const d = () => {
                (o = null), (i = !1), (l = !1), (r = null), (s = null), (a = null), n.debug("Global scope was reset");
            };
            let u = 0;
            class c extends E.Pointer {
                point(e) {
                    if (o) return n.debug("New area will be not created because there is already exists another active one:", o), this.getEmptyPoint();
                    if (this.shouldBeIgnored(e)) return n.debug("New area will be not created because of ignoring"), this.getEmptyPoint();
                    d(), (o = this.create(e)), (r = e), (i = !0), n.debug("New area created:", o);
                    const t = this.createPoint(o);
                    return this.setLastPoint(t), this.getLastPoint();
                }
                restore(e) {
                    if (null == e.width || null == e.height || null == e.left || null == e.top) return this.getEmptyPoint();
                    const t = this.create({ clientX: 0, clientY: 0, scrollX: 0, scrollY: 0 });
                    return t ? (h({ areaElement: t, width: e.width, height: e.height, left: e.left, top: e.top, updateActiveBlur: !1 }), this.createPoint(t)) : this.getEmptyPoint();
                }
                create(e) {
                    const t = document.createElement("div"),
                        r = document.createElement("div"),
                        d = document.createElement("div"),
                        u = document.createElement("div");
                    t.classList.add("custom-area-pointer-point"),
                        r.classList.add("custom-area-pointer-point-container"),
                        d.classList.add("custom-area-pointer-point-dimensions-change"),
                        u.classList.add("custom-area-pointer-point-position-change"),
                        k(t),
                        k(r),
                        k(d),
                        k(u),
                        (d.title = "Resize"),
                        (u.title = "Move");
                    let c = 0,
                        g = 0,
                        f = e.clientX + e.scrollX,
                        b = e.clientY + e.scrollY;
                    return (
                        ({ width: c, height: g } = m({ width: c, height: g, minWidth: 0, minHeight: 0, left: f, top: b })),
                        ({ left: f, top: b } = p({ width: c, height: g, left: f, top: b })),
                        h({ areaElement: t, width: c, height: g, left: f, top: b, updateActiveBlur: !1 }),
                        d.addEventListener("mousedown", (e) => {
                            e.stopPropagation(), (i = !0), (s = e), (o = t), n.debug("Enabled dimensions change");
                        }),
                        u.addEventListener("mousedown", (e) => {
                            e.stopPropagation(), (l = !0), (a = e), (o = t), n.debug("Enabled position change");
                        }),
                        t.addEventListener("mouseenter", () => {
                            o !== t && (null == o ? ((o = t), n.debug("Active:", t)) : n.debug("Area will not become active because previous area is still active:", t));
                        }),
                        t.addEventListener("mouseleave", () => {
                            i || l ? n.debug("Area will not become inactive because some handler is active:", t) : ((o = null), n.debug("Inactive:", t));
                        }),
                        r.appendChild(u),
                        r.appendChild(d),
                        t.appendChild(r),
                        document.body
                            ? document.body.appendChild(t)
                            : window.setTimeout(() => {
                                  document.body.appendChild(t);
                              }, 250),
                        t
                    );
                }
                read() {
                    throw new Error("Not supported");
                }
                on() {
                    document.body.classList.add(t), super.on();
                }
                off() {
                    document.body.classList.remove(t), super.off();
                }
                createPoint(e) {
                    if (!e) return this.getEmptyPoint();
                    const t = new x();
                    return (
                        (t.type = "area"),
                        (t.element = e),
                        (t.isTransparent = !0),
                        (t.shouldBeRemovedFromDOM = !0),
                        (t.classNameToHide = "custom-area-pointer-point-hide"),
                        (t.classNameToHideControllers = "custom-area-pointer-point-hide-controllers"),
                        (t.classNameToAddOutline = "custom-area-pointer-point-outline"),
                        e.clientWidth ? (t.width = e.clientWidth) : (t.width = Number(e.style.width.replace("px", ""))),
                        e.clientHeight ? (t.height = e.clientHeight) : (t.height = Number(e.style.height.replace("px", ""))),
                        e.offsetLeft ? (t.left = e.offsetLeft) : (t.left = Number(e.style.left.replace("px", ""))),
                        e.offsetTop ? (t.top = e.offsetTop) : (t.top = Number(e.style.top.replace("px", ""))),
                        t
                    );
                }
                shouldBeIgnored(e) {
                    const t = document.elementFromPoint(e.clientX, e.clientY);
                    return !!(t instanceof HTMLElement && H(t));
                }
            }
            function h(e) {
                const { width: t, height: n, left: o, top: i, areaElement: l, updateActiveBlur: r } = e;
                (l.style.width = `${t}px`),
                    (l.style.height = `${n}px`),
                    (l.style.left = `${o}px`),
                    (l.style.top = `${i}px`),
                    r &&
                        (window.clearTimeout(u),
                        (u = window.setTimeout(() => {
                            const e = new x();
                            (e.type = "area"), (e.element = l);
                            const r = y.findWithPoint(e);
                            r && ((r.point.width = t), (r.point.height = n), (r.point.left = o), (r.point.top = i), y.setPoint(r.id, r.point));
                        }, 250)));
            }
            function m(e) {
                const { left: t, top: n, minWidth: o, minHeight: i } = e;
                let { width: l, height: r } = e;
                if (!document.body) return { width: l, height: r };
                const s = document.body.scrollWidth,
                    a = document.body.scrollHeight;
                return l < o && (l = o), r < i && (r = i), l + t > s && (l = s - t), r + n > a && (r = a - n), { width: l, height: r };
            }
            function p(e) {
                const { width: t, height: n } = e;
                let { left: o, top: i } = e;
                if (!document.body) return { left: o, top: i };
                const l = document.body.scrollWidth,
                    r = document.body.scrollHeight;
                return o < 0 && (o = 0), i < 0 && (i = 0), o + t > l && (o = l - t), i + n > r && (i = r - n), { left: o, top: i };
            }
            (e.Pointer = c),
                document.addEventListener("mousemove", (e) => {
                    if (!o) return;
                    e.preventDefault();
                    const t = o.getBoundingClientRect(),
                        d = t.width,
                        u = t.height,
                        c = t.left + window.scrollX,
                        g = t.top + window.scrollY;
                    let f = d,
                        b = u,
                        v = c,
                        w = g;
                    i
                        ? (({ width: f, height: b } = (function (e) {
                              const { currentEvent: t, lastEvent: o, lastCreateArgs: i, currentWidth: l, currentHeight: r } = e;
                              let s = l,
                                  a = r;
                              return (
                                  o ? ((s += t.clientX - o.clientX), (a += t.clientY - o.clientY)) : i ? ((s = t.clientX - i.clientX), (a = t.clientY - i.clientY)) : n.warn("Invalid arguments for dimensions change"), { width: s, height: a }
                              );
                          })({ currentEvent: e, lastEvent: s, lastCreateArgs: r, currentWidth: f, currentHeight: b })),
                          ({ width: f, height: b } = m({ width: f, height: b, minWidth: 0, minHeight: 0, left: v, top: w })),
                          (s = e))
                        : l &&
                          (({ left: v, top: w } = (function (e) {
                              const { currentEvent: t, lastEvent: o, currentLeft: i, currentTop: l } = e;
                              let r = i,
                                  s = l;
                              return o ? ((r = i + t.pageX - o.pageX), (s = l + t.pageY - o.pageY)) : n.warn("Invalid arguments for position change"), { left: r, top: s };
                          })({ currentEvent: e, lastEvent: a, currentLeft: v, currentTop: w })),
                          ({ left: v, top: w } = p({ width: f, height: b, left: v, top: w })),
                          (a = e)),
                        (f !== d || b !== u || v !== c || w !== g) && h({ areaElement: o, width: f, height: b, left: v, top: w, updateActiveBlur: !0 });
                }),
                document.addEventListener("mouseup", () => {
                    if (!o) return;
                    const { width: e, height: t } = o.getBoundingClientRect();
                    if (0 === e || 0 === t) {
                        const e = o;
                        n.debug("Area will be automatically removed");
                        const t = new x();
                        (t.type = "area"), (t.element = e);
                        const i = y.findWithPoint(t);
                        i && (y.disable(i.id), y.remove(i.id));
                    }
                    d();
                });
        })(A || (A = {})),
        (function (e) {
            class t extends E.Pointer {
                point(e) {
                    let t = this.read(e);
                    return t || (n.debug("Unable to read for text point"), (t = this.create(e))), t || n.debug("Unable to create for text point"), this.getEmptyPoint();
                }
                restore(e) {
                    return this.getEmptyPoint();
                }
                create(e) {
                    return null;
                }
                read(e) {
                    var t, n;
                    const { commonAncestorContainer: o, startContainer: i, endContainer: l, startOffset: r, endOffset: s } = e.range;
                    return "style" in o && o.firstChild === i && o.lastChild === l && 0 === r && s === (l.textContent || "").length
                        ? o
                        : (null === (t = i.parentElement) || void 0 === t ? void 0 : t.firstChild) === i && 0 === r && (null === (n = i.parentElement) || void 0 === n ? void 0 : n.nextElementSibling) === l && 0 === s
                        ? i.parentElement
                        : null;
                }
            }
            e.Pointer = t;
        })(L || (L = {}));
    const M = C.Pointer,
        I = A.Pointer;
    var N;
    L.Pointer,
        (function (e) {
            (e.None = "None"), (e.PointToElement = "PointToElement"), (e.PointToArea = "PointToArea");
        })(N || (N = {}));
    let z,
        O,
        $ = l(),
        D = r(),
        R = {},
        V = -1,
        S = -1,
        U = N.None,
        Y = { [N.None]: void 0, [N.PointToElement]: new M(), [N.PointToArea]: new I() },
        X = null,
        W = null,
        F = !1,
        q = [];
    function G() {
        -1 !== V && j(),
            b.hide(),
            (V = window.setTimeout(() => {
                j(), n.warn("Document was unhidden due to timeout");
            }, 3e3));
    }
    function j() {
        b.unhide(), window.clearTimeout(V), (V = -1);
    }
    function J() {
        -1 !== S && K(),
            b.blur(),
            (S = window.setTimeout(() => {
                K(), n.warn("Document was unblurred due to timeout");
            }, 3e3));
    }
    function K() {
        b.unblur(), window.clearTimeout(S), (S = -1);
    }
    async function Q() {
        O && (n.debug("Restore promise rejected"), O()), (q = []);
        const e = new Promise((e, t) => {
                (z = e), (O = t);
            }),
            t = () => {
                0 === q.length && (z && z(), (z = void 0), (O = void 0));
            };
        let o,
            i,
            l = [];
        l.push(
            m.read($).then((e) => {
                o = e;
            })
        ),
            l.push(
                p.read(D, !0).then((e) => {
                    i = e;
                })
            ),
            await Promise.all(l).catch((e) => {
                n.error(e);
            }),
            (l = []);
        const r = [...(o || []), ...(i || [])];
        if (!r || !r.length) return n.debug("Nothing to restore"), t(), e;
        for (const e of r)
            l.push(
                h.read(e, !0).then((e) => {
                    if (!e) return;
                    const t = e.data;
                    Z(t) || (q.push(t), n.debug("Added to pending restore:", t), n.debug(q));
                })
            );
        try {
            await Promise.all(l);
        } catch (e) {
            n.error(e);
        }
        return t(), e;
    }
    function Z(e) {
        const { id: t } = e,
            o = new y.Data();
        try {
            o.deserialize(e);
        } catch (o) {
            return n.warn("Unable to deserialize active blur data:", e), n.error(o), y.remove(t), !1;
        }
        let { point: i } = o;
        switch (i.type) {
            case "element":
                i = Y[N.PointToElement].restore(i);
                break;
            case "area":
                i = Y[N.PointToArea].restore(i);
                break;
            default:
                return !1;
        }
        return "empty" === i.type ? (n.debug("Restored active blur point is empty:", e), !1) : ((o.point = i), n.debug("Active blur will be restored:", e), y.restore(o), !0);
    }
    function _(e) {
        var t, o;
        (X = null), null === (t = Y[U]) || void 0 === t || t.off(), e === U ? ((U = N.None), n.debug(`${e} disabled`)) : ((U = e), null === (o = Y[U]) || void 0 === o || o.on(), n.debug(`${e} enabled`));
    }
    function ee() {
        n.debug("Edit mode on"),
            (F = !0),
            y.unhideControllersAll(),
            y.disableInteractionBlockAll(),
            P.remove(),
            P.create({
                onToggleElementBlurButtonClick: () => {
                    _(N.PointToElement);
                },
                onToggleAreaBlurButtonClick: () => {
                    _(N.PointToArea);
                },
                onToggleDocumentTitleBlurButtonClick: () => {
                    v.isBlurred() ? v.unblur() : v.blur();
                },
                onRemoveAllBlurButtonClick: () => {
                    if (0 !== q.length) {
                        n.warn("Every not restored blur also will be removed");
                        for (const e of q) {
                            const t = new x();
                            y.push(e.id, t, !1);
                        }
                        q = [];
                    }
                    y.disableAll(!1), y.removeAll(), v.isBlurred() && v.unblur();
                },
                onIncreaseBlurIntensityButtonClick: () => {
                    if (!y.exists()) return;
                    const e = g + 1;
                    e > 25 || (P.setCurrentBlurIntensityText(e), y.setIntensityAll(e), y.enableAll(!1));
                },
                onDecreaseBlurIntensityButtonClick: () => {
                    if (!y.exists()) return;
                    const e = g - 1;
                    e < 0 || (P.setCurrentBlurIntensityText(e), y.setIntensityAll(e), y.enableAll(!1));
                },
                onCloseButtonClick: () => {
                    te();
                },
            }),
            P.setCurrentBlurIntensityText(g);
    }
    function te() {
        n.debug("Edit mode off"), (F = !1), _(N.None), y.hideControllersAll(), y.enableInteractionBlockAll(), P.remove();
    }
    function ne() {
        F ? te() : ee();
    }
    function oe() {
        F ? ee() : te();
    }
    let ie = !1;
    function le() {
        ie && n.warn("Blur manager will be launched more than one time.", "It is not logic error?");
        let e = !1;
        new Promise((e) => {
            if (document.body) return G(), void e();
            new MutationObserver((t, n) => {
                for (const o of t)
                    if ("BODY" === o.target.nodeName) {
                        G(), e(), n.disconnect();
                        break;
                    }
            }).observe(document, { subtree: !0, childList: !0 });
        }).then(() => {
            e && j();
        });
        const t = window.setTimeout(() => {
            J();
        }, 2e3);
        !(function (o) {
            const i = () => {
                Q()
                    .then(() => {
                        (e = !0), window.clearTimeout(t), j(), K();
                    })
                    .catch(() => {
                        n.warn("Unable to restore every blur");
                    })
                    .finally(() => {
                        oe();
                    });
            };
            "loading" === document.readyState ? document.addEventListener("DOMContentLoaded", i) : i();
        })(),
            new MutationObserver(() => {
                const e = l();
                e !== $ &&
                    (n.debug("URL change detected"),
                    ($ = e),
                    (D = r()),
                    (async function () {
                        J(),
                            y.disableInteractionBlockAll(),
                            y.disableAll(!1),
                            y.removeAll(!1),
                            await Q()
                                .then(() => {
                                    K();
                                })
                                .catch(() => {
                                    n.warn("Unable to restore every blur");
                                })
                                .finally(() => {
                                    oe();
                                });
                    })());
            }).observe(document, { subtree: !0, childList: !0 }),
            new MutationObserver(() => {
                if (0 === q.length) return;
                const e = [];
                for (const t of q) Z(t) ? (n.debug("Removed from pending restore:", t), n.debug(q)) : e.push(t);
                (q = e), 0 === q.length && (n.debug("All pending data was restored"), z && z(), (z = void 0), (O = void 0));
            }).observe(document, { subtree: !0, childList: !0 }),
            document.addEventListener("mousemove", (e) => {
                if ((U !== N.None && e.preventDefault(), U === N.PointToElement)) {
                    const t = Y[N.PointToElement].point({ clientX: e.clientX, clientY: e.clientY });
                    if (t === X) return;
                    (X = t), n.debug("New element point:", t);
                }
            }),
            document.addEventListener("mousedown", (e) => {
                if ((U !== N.None && e.preventDefault(), U === N.PointToArea)) {
                    const t = Y[N.PointToArea].point({ clientX: e.clientX, clientY: e.clientY, scrollX: window.scrollX, scrollY: window.scrollY });
                    if (t === X) return;
                    if (((X = t), n.debug("New area point:", t), "empty" === t.type)) return;
                    w.push(X), w.release(), w.clear();
                }
            }),
            document.addEventListener(
                "click",
                (e) => {
                    const t = U !== N.None && X && "empty" !== X.type;
                    t && (e.stopPropagation(), e.preventDefault(), n.debug("Click event in capturing phase was stopped")), t && U !== N.PointToArea && (w.push(X), w.release(), w.clear());
                },
                !0
            ),
            document.addEventListener("contextmenu", (e) => {
                W = e;
            }),
            chrome.runtime.sendMessage({ type: "getAllShortcuts" }, (e) => {
                e.data && (R = e.data);
            }),
            (ie = !0),
            n.debug("Blur manager launched");
    }
    !(async function () {
        chrome.runtime.onMessage.addListener((e, t, o) => {
            ie ||
                ((function (e) {
                    return ["toggleEditMode", "disableEditMode", "handleShortcut", "enableAllActiveBlur", "disableAllActiveBlur", "blurCurrentElement"].includes(e.type);
                })(e) &&
                    (n.debug("Blur manager required to be launched"), le())),
                (function (e, t) {
                    switch (e.type) {
                        case "getCurrentHostname":
                            t({ type: "getCurrentHostname", data: D });
                            break;
                        case "toggleEditMode":
                            ne();
                            break;
                        case "disableEditMode":
                            te();
                            break;
                        case "handleShortcut":
                            e.data
                                ? (function (e) {
                                      switch ((n.debug("Shortcut will be handled -", e), e)) {
                                          case "toggleEditMode":
                                              ne();
                                              break;
                                          case "togglePointToElement":
                                              F || ee(), _(N.PointToElement);
                                              break;
                                          case "togglePointToArea":
                                              F || ee(), _(N.PointToArea);
                                              break;
                                          case "toggleDocumentTitleBlur":
                                              v.isBlurred() ? v.unblur() : v.blur();
                                      }
                                  })(e.data)
                                : n.warn("No message data");
                            break;
                        case "enableAllActiveBlur":
                            y.enableAll(!1), y.enableInteractionBlockAll();
                            break;
                        case "disableAllActiveBlur":
                            y.disableAll(!1), y.disableInteractionBlockAll();
                            break;
                        case "blurCurrentElement": {
                            if (!W) {
                                n.warn("No last context menu event");
                                break;
                            }
                            U !== N.PointToElement && _(N.PointToElement);
                            const e = Y[N.PointToElement].point({ clientX: W.clientX, clientY: W.clientY });
                            if (((W = null), "empty" === e.type)) {
                                ee();
                                break;
                            }
                            w.push(e), w.release(), w.clear(), F || te();
                            break;
                        }
                    }
                })(e, o);
        }),
            (await (async function () {
                const { blurManagerDisabled: e, blurManagerDisabledHostnames: t } = await o({ blurManagerDisabled: !1, blurManagerDisabledHostnames: [] }),
                    n = r();
                return !!e || !!t.includes(n);
            })())
                ? n.debug("Blur manager exited")
                : le();
    })();
})();
