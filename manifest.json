{"update_url": "https://clients2.google.com/service/update2/crx", "manifest_version": 3, "name": "ZeroBlur", "short_name": "ZeroBlur", "description": "Blur any sensitive information from any website or web app. Keep data safe when delivering demos or recording videos!", "version": "1.2.1", "author": "DivByZero", "action": {"default_title": "ZeroBlur", "default_popup": "popup.html", "default_icon": {"16": "./static/logo/16.png", "32": "./static/logo/32.png", "48": "./static/logo/48.png", "128": "./static/logo/128.png"}}, "icons": {"16": "./static/logo/16.png", "32": "./static/logo/32.png", "48": "./static/logo/48.png", "128": "./static/logo/128.png"}, "content_scripts": [{"js": ["blur-manager.js"], "css": ["blur-manager.css"], "matches": ["<all_urls>"], "run_at": "document_start", "all_frames": false}], "background": {"service_worker": "background.js"}, "commands": {"toggleEditMode": {"description": "Toggles editing of blur on current page.", "suggested_key": {"default": "Alt+Shift+1"}}, "togglePointToElement": {"description": "Toggles blurring of page element.", "suggested_key": {"default": "Alt+Shift+2"}}, "togglePointToArea": {"description": "Toggles blurring of arbitrary area.", "suggested_key": {"default": "Alt+Shift+3"}}, "toggleDocumentTitleBlur": {"description": "Toggles blurring of document title and favicon.", "suggested_key": {"default": "Alt+Shift+4"}}}, "web_accessible_resources": [{"resources": ["*.ico"], "matches": ["<all_urls>"]}], "permissions": ["storage", "contextMenus"], "content_security_policy": {"extension_pages": "default-src 'self'; style-src 'self' 'unsafe-inline';"}, "offline_enabled": false, "minimum_chrome_version": "88"}