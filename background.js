(()=>{"use strict";const e=Boolean(!0);function t(){return e}var n;!function(e){e[e.TRACE=0]="TRACE",e[e.DEBUG=1]="DEBUG",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(n||(n={}));const o=new class{constructor(){this.logLevel=t()?n.INFO:n.TRACE}setLevel(e){this.logLevel=e}error(...e){this.logLevel<=n.ERROR&&console.error(...e)}warn(...e){this.logLevel<=n.WARN&&console.warn(...e)}info(...e){this.logLevel<=n.INFO&&console.info(...e)}debug(...e){this.logLevel<=n.DEBUG&&console.debug(...e)}trace(...e){this.logLevel<=n.TRACE&&console.trace(...e)}},a={extensionInstallDate:"",extensionUpdateDate:"",buildInfoTarget:"chrome",buildInfoIsProduction:e,buildInfoNodeEnv:"production",buildInfoCommitHash:"f33045b922c5fa415d8830fb0754149aab388f5b",buildInfoBuildDate:"2021-12-31T12:21:34.214Z",blurManagerDisabled:!1,blurManagerDisabledHostnames:[],blurManagerItems:{},blurManagerItemsOfPathname:{},blurManagerItemsOfHostname:{}};async function r(e){return new Promise((t=>{chrome.storage.local.set(e,(()=>t()))}))}async function c(e){return new Promise((t=>{chrome.storage.local.get(e,(e=>t(e)))}))}async function s(e,t){await r({[e]:t})}async function i(e=null){const t=e||(new Date).toISOString();return await s("extensionUpdateDate",t),t}function l(){chrome.contextMenus.create({id:"blur-element",title:"Blur element",contexts:["all"]})}chrome.runtime.onInstalled.addListener((async e=>{"install"===e.reason?await async function(){t()&&chrome.tabs.create({url:"https://divbyzero.com/tools/blur-extension/?src=welcome&target=chrome"});try{await async function(){await async function(){return new Promise((e=>{chrome.storage.local.clear((()=>e()))}))}(),await r(a)}()}catch(e){throw alert("Please reinstall the extension"),e}try{const e=await async function(){const e=(new Date).toISOString();return await s("extensionInstallDate",e),e}();await i(e)}catch(e){o.error(e)}try{l()}catch(e){o.error(e)}}():"update"===e.reason&&await async function(){t()&&chrome.tabs.create({url:"https://divbyzero.com/tools/blur-extension/?src=latest-update&target=chrome"});try{await async function(){const e=await c(null);for(const t in a){if(Object.prototype.hasOwnProperty.call(e,t))continue;const n=a[t];await s(t,n)}}()}catch(e){o.error(e)}try{await i()}catch(e){o.error(e)}try{await async function(){const e=["buildInfoTarget","buildInfoIsProduction","buildInfoNodeEnv","buildInfoCommitHash","buildInfoBuildDate"],t=await c(null),n={};let o=!1;for(const r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;if(-1===e.indexOf(r))continue;const c=a[r];c!==t[r]&&(n[r]=c,o=!0)}o&&await r(n)}()}catch(e){o.error(e)}try{l()}catch(e){o.error(e)}}()})),chrome.commands.onCommand.addListener(((e,t)=>{t.id?chrome.tabs.sendMessage(t.id,{type:"handleShortcut",data:e}):o.warn("Unknown tab ID, can't handle shortcut")})),chrome.runtime.onMessage.addListener(((e,t,n)=>{switch(e.type){case"getAllShortcuts":return async function(){return new Promise((e=>{chrome.commands.getAll((t=>{const n={};for(const e of t){const{name:t,description:o,shortcut:a}=e;t&&(n[t]={description:o,shortcut:a})}e(n)}))}))}().then((e=>{n({type:"getAllShortcuts",data:e})})),!0}return!1})),chrome.contextMenus.onClicked.addListener((async e=>{switch(e.menuItemId){case"blur-element":{const e=await async function(){return new Promise((e=>{chrome.tabs.query({active:!0,currentWindow:!0},(t=>{if(!t.length)return e(void 0);e(t[0].id)}))}))}();if(null==e){o.warn("Tab ID is undefined");break}chrome.tabs.sendMessage(e,{type:"blurCurrentElement"});break}default:o.debug("Unknown context menu ID")}}))})();